/*
###
Description:    Batch compress existing images by checking size and compressing if needed

Usage:         ./start.sh -n compressPic -d "goresodownload" -cmd "cmd/batch/compressPic/main.go -dir=/path/to/images -size=200 -dryrun"

Create date:    2025-08-22
Author:         <PERSON><PERSON> x<PERSON>owei
Run frequency:  One-off or as needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gofile "github.com/real-rm/gofile"
	golog "github.com/real-rm/golog"
	gospeedmeter "github.com/real-rm/gospeedmeter"
)

var (
	dirFlag        = flag.String("dir", "", "Directory path to process images (required)")
	sizeFlag       = flag.Int("size", 200, "Target size limit in KB (default: 200)")
	maxDimFlag     = flag.Int("maxdim", 1280, "Maximum dimension (width or height) in pixels (default: 1280)")
	recursiveFlag  = flag.Bool("recursive", true, "Process subdirectories recursively (default: true)")
	dryrunFlag     = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	concurrentFlag = flag.Int("concurrent", 1, "Number of concurrent processing goroutines (default: 1)")

	speedMeter *gospeedmeter.SpeedMeter
	startTime  = time.Now()
)

// Supported image extensions
var supportedExtensions = map[string]bool{
	".jpg":  true,
	".jpeg": true,
	".png":  true,
}

// Statistics for processing
type ProcessStats struct {
	TotalFiles      int64
	ProcessedFiles  int64
	CompressedFiles int64
	SkippedFiles    int64
	ErrorFiles      int64
	TotalSizeBefore int64
	TotalSizeAfter  int64
	mutex           sync.Mutex
}

func (s *ProcessStats) AddFile(sizeBefore, sizeAfter int64, compressed bool, hasError bool) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.TotalFiles++
	s.TotalSizeBefore += sizeBefore

	if hasError {
		s.ErrorFiles++
		return
	}

	s.ProcessedFiles++
	s.TotalSizeAfter += sizeAfter

	if compressed {
		s.CompressedFiles++
	} else {
		s.SkippedFiles++
	}
}

func (s *ProcessStats) GetStats() (int64, int64, int64, int64, int64, int64, int64) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	return s.TotalFiles, s.ProcessedFiles, s.CompressedFiles, s.SkippedFiles, s.ErrorFiles, s.TotalSizeBefore, s.TotalSizeAfter
}

func init() {
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})
}

// isImageFile checks if the file has a supported image extension
func isImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return supportedExtensions[ext]
}

// getFileSizeKB returns file size in KB
func getFileSizeKB(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size() / 1024, nil
}

// processImageFile processes a single image file
func processImageFile(filePath string, targetSizeKB int, stats *ProcessStats) error {
	speedMeter.Check("processed", 1)

	// Get original file size
	originalSizeKB, err := getFileSizeKB(filePath)
	if err != nil {
		golog.Error("Failed to get file size", "file", filePath, "error", err)
		stats.AddFile(0, 0, false, true)
		return fmt.Errorf("failed to get file size for %s: %w", filePath, err)
	}

	golog.Debug("Processing image", "file", filePath, "originalSizeKB", originalSizeKB, "targetSizeKB", targetSizeKB)

	// Check if compression is needed
	if originalSizeKB <= int64(targetSizeKB) {
		golog.Debug("Skipping file - already within target size", "file", filePath, "sizeKB", originalSizeKB)
		stats.AddFile(originalSizeKB*1024, originalSizeKB*1024, false, false)
		speedMeter.Check("skipped", 1)
		return nil
	}

	if *dryrunFlag {
		golog.Info("Dry run: would compress image", "file", filePath, "originalSizeKB", originalSizeKB, "targetSizeKB", targetSizeKB)
		stats.AddFile(originalSizeKB*1024, originalSizeKB*1024, true, false)
		speedMeter.Check("dryrun", 1)
		return nil
	}

	// Read file data
	imageData, err := os.ReadFile(filePath)
	if err != nil {
		golog.Error("Failed to read image file", "file", filePath, "error", err)
		stats.AddFile(originalSizeKB*1024, 0, false, true)
		return fmt.Errorf("failed to read image file %s: %w", filePath, err)
	}

	// Process image with compression
	processStart := time.Now()
	processedData, err := gofile.ProcessImageDataWithResize(imageData, targetSizeKB)
	processDuration := time.Since(processStart)

	if err != nil {
		golog.Error("Failed to process image", "file", filePath, "duration", processDuration, "error", err)
		stats.AddFile(originalSizeKB*1024, 0, false, true)
		return fmt.Errorf("failed to process image %s: %w", filePath, err)
	}

	// Write processed data back to file
	err = os.WriteFile(filePath, processedData, 0644)
	if err != nil {
		golog.Error("Failed to write processed image", "file", filePath, "error", err)
		stats.AddFile(originalSizeKB*1024, 0, false, true)
		return fmt.Errorf("failed to write processed image %s: %w", filePath, err)
	}

	newSizeKB := int64(len(processedData)) / 1024
	compressionRatio := float64(len(processedData)) / float64(len(imageData))

	golog.Info("Successfully compressed image",
		"file", filePath,
		"originalSizeKB", originalSizeKB,
		"newSizeKB", newSizeKB,
		"compressionRatio", fmt.Sprintf("%.2f", compressionRatio),
		"duration", processDuration)

	stats.AddFile(originalSizeKB*1024, int64(len(processedData)), true, false)
	speedMeter.Check("compressed", 1)

	// Clear memory
	imageData = nil
	processedData = nil

	return nil
}

// scanDirectory scans directory for image files
func scanDirectory(dirPath string, recursive bool) ([]string, error) {
	var imageFiles []string

	if recursive {
		err := filepath.WalkDir(dirPath, func(path string, d os.DirEntry, err error) error {
			if err != nil {
				golog.Warn("Error accessing path", "path", path, "error", err)
				return nil // Continue processing other files
			}

			if !d.IsDir() && isImageFile(d.Name()) {
				imageFiles = append(imageFiles, path)
			}
			return nil
		})
		return imageFiles, err
	} else {
		// Non-recursive: only scan the specified directory
		entries, err := os.ReadDir(dirPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read directory %s: %w", dirPath, err)
		}

		for _, entry := range entries {
			if !entry.IsDir() && isImageFile(entry.Name()) {
				fullPath := filepath.Join(dirPath, entry.Name())
				imageFiles = append(imageFiles, fullPath)
			}
		}
		return imageFiles, nil
	}
}

// worker processes image files from a channel
func worker(ctx context.Context, id int, jobs <-chan string, targetSizeKB int, stats *ProcessStats, wg *sync.WaitGroup) {
	defer wg.Done()

	for {
		select {
		case filePath, ok := <-jobs:
			if !ok {
				golog.Debug("Worker finished", "workerID", id)
				return
			}

			golog.Debug("Worker processing file", "workerID", id, "file", filePath)
			err := processImageFile(filePath, targetSizeKB, stats)
			if err != nil {
				golog.Error("Worker failed to process file", "workerID", id, "file", filePath, "error", err)
				// According to requirements: exit on failure
				golog.Fatal("Processing failed, exiting as requested", "error", err)
			}

		case <-ctx.Done():
			golog.Debug("Worker cancelled", "workerID", id)
			return
		}
	}
}

// printStats prints processing statistics
func printStats(stats *ProcessStats) {
	total, processed, compressed, skipped, errors, sizeBefore, sizeAfter := stats.GetStats()

	golog.Info("====== Processing Statistics ======")
	golog.Info("Files processed", "total", total, "processed", processed, "compressed", compressed, "skipped", skipped, "errors", errors)

	if sizeBefore > 0 {
		savedBytes := sizeBefore - sizeAfter
		savedMB := float64(savedBytes) / (1024 * 1024)
		compressionRatio := float64(sizeAfter) / float64(sizeBefore)

		golog.Info("Size statistics",
			"sizeBefore_MB", fmt.Sprintf("%.2f", float64(sizeBefore)/(1024*1024)),
			"sizeAfter_MB", fmt.Sprintf("%.2f", float64(sizeAfter)/(1024*1024)),
			"saved_MB", fmt.Sprintf("%.2f", savedMB),
			"compressionRatio", fmt.Sprintf("%.2f", compressionRatio))
	}

	duration := time.Since(startTime)
	golog.Info("Processing completed", "duration", duration.String(), "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
}

// run executes the main processing logic
func run(ctx context.Context) error {
	flag.Parse()

	// Validate required parameters
	if *dirFlag == "" {
		return fmt.Errorf("directory path is required (use -dir flag)")
	}

	// Check if directory exists
	if _, err := os.Stat(*dirFlag); os.IsNotExist(err) {
		return fmt.Errorf("directory does not exist: %s", *dirFlag)
	}

	golog.Info("Starting image compression batch",
		"directory", *dirFlag,
		"targetSizeKB", *sizeFlag,
		"maxDimension", *maxDimFlag,
		"recursive", *recursiveFlag,
		"dryrun", *dryrunFlag,
		"concurrent", *concurrentFlag)

	// Scan directory for image files
	golog.Info("Scanning directory for image files...")
	imageFiles, err := scanDirectory(*dirFlag, *recursiveFlag)
	if err != nil {
		return fmt.Errorf("failed to scan directory: %w", err)
	}

	if len(imageFiles) == 0 {
		golog.Info("No image files found in directory", "directory", *dirFlag)
		return nil
	}

	golog.Info("Found image files", "count", len(imageFiles))

	// Initialize statistics
	stats := &ProcessStats{}

	// Create job channel
	jobs := make(chan string, len(imageFiles))

	// Start workers
	var wg sync.WaitGroup
	numWorkers := *concurrentFlag
	if numWorkers < 1 {
		numWorkers = 1
	}

	golog.Info("Starting workers", "count", numWorkers)
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go worker(ctx, i+1, jobs, *sizeFlag, stats, &wg)
	}

	// Send jobs to workers
	go func() {
		defer close(jobs)
		for _, filePath := range imageFiles {
			select {
			case jobs <- filePath:
			case <-ctx.Done():
				golog.Info("Context cancelled, stopping job distribution")
				return
			}
		}
	}()

	// Wait for all workers to complete
	wg.Wait()

	// Print final statistics
	printStats(stats)

	return nil
}

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	golog.Info("Starting compressPic batch")
	if err := run(ctx); err != nil {
		golog.Fatal("compressPic failed", "error", err)
	}
	golog.Info("compressPic completed successfully")
}
